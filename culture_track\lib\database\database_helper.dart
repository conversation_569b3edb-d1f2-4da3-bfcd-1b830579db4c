import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';

/// Database helper class for managing SQLite database operations
/// Implements singleton pattern for single database instance
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  // Database configuration
  static const String _databaseName = 'culture_track.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String tableCultures = 'cultures';
  static const String tableRecipes = 'recipes';
  static const String tableTransferLogs = 'transfer_logs';
  static const String tableNotifications = 'notifications';

  DatabaseHelper._internal();

  factory DatabaseHelper() {
    return _instance;
  }

  /// Get database instance, create if doesn't exist
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database with proper configuration
  Future<Database> _initDatabase() async {
    // Get the documents directory path
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);

    // Open/create the database
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onConfigure: _onConfigure,
    );
  }

  /// Configure database settings (foreign keys, etc.)
  Future<void> _onConfigure(Database db) async {
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
  }

  /// Create database tables on first run
  Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
    await _createIndexes(db);
    await _seedInitialData(db);
  }

  /// Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here
    // For now, we'll just recreate tables (data loss)
    // In production, implement proper migration logic
    if (oldVersion < newVersion) {
      await _dropTables(db);
      await _createTables(db);
      await _createIndexes(db);
      await _seedInitialData(db);
    }
  }

  /// Create all database tables
  Future<void> _createTables(Database db) async {
    // Create cultures table
    await db.execute('''
      CREATE TABLE $tableCultures (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        species TEXT NOT NULL,
        variety TEXT,
        source TEXT,
        creation_date INTEGER NOT NULL,
        current_stage TEXT NOT NULL CHECK (current_stage IN ('initiation', 'multiplication', 'rooting', 'acclimatization')),
        status TEXT NOT NULL CHECK (status IN ('active', 'contaminated', 'transferred', 'completed', 'failed')),
        last_transfer_date INTEGER,
        next_transfer_date INTEGER,
        notes TEXT,
        photos TEXT,
        contamination_log TEXT,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      )
    ''');

    // Create recipes table
    await db.execute('''
      CREATE TABLE $tableRecipes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        media_type TEXT NOT NULL,
        ingredients_json TEXT NOT NULL,
        instructions TEXT,
        source TEXT,
        is_template INTEGER NOT NULL DEFAULT 0 CHECK (is_template IN (0, 1)),
        created_date INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      )
    ''');

    // Create transfer_logs table
    await db.execute('''
      CREATE TABLE $tableTransferLogs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        culture_id INTEGER NOT NULL,
        from_stage TEXT,
        to_stage TEXT NOT NULL,
        transfer_date INTEGER NOT NULL,
        notes TEXT,
        photos TEXT,
        success_indicator INTEGER CHECK (success_indicator IN (0, 1)),
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (culture_id) REFERENCES $tableCultures (id) ON DELETE CASCADE
      )
    ''');

    // Create notifications table
    await db.execute('''
      CREATE TABLE $tableNotifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        culture_id INTEGER NOT NULL,
        notification_type TEXT NOT NULL CHECK (notification_type IN ('transfer_reminder', 'contamination_alert', 'stage_update')),
        scheduled_date INTEGER NOT NULL,
        is_sent INTEGER NOT NULL DEFAULT 0 CHECK (is_sent IN (0, 1)),
        is_dismissed INTEGER NOT NULL DEFAULT 0 CHECK (is_dismissed IN (0, 1)),
        title TEXT NOT NULL,
        body TEXT NOT NULL,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (culture_id) REFERENCES $tableCultures (id) ON DELETE CASCADE
      )
    ''');
  }

  /// Create database indexes for performance
  Future<void> _createIndexes(Database db) async {
    // Culture indexes
    await db.execute(
      'CREATE INDEX idx_cultures_status ON $tableCultures (status)',
    );
    await db.execute(
      'CREATE INDEX idx_cultures_stage ON $tableCultures (current_stage)',
    );
    await db.execute(
      'CREATE INDEX idx_cultures_next_transfer ON $tableCultures (next_transfer_date)',
    );
    await db.execute(
      'CREATE INDEX idx_cultures_created ON $tableCultures (creation_date)',
    );

    // Recipe indexes
    await db.execute(
      'CREATE INDEX idx_recipes_media_type ON $tableRecipes (media_type)',
    );
    await db.execute(
      'CREATE INDEX idx_recipes_template ON $tableRecipes (is_template)',
    );

    // Transfer log indexes
    await db.execute(
      'CREATE INDEX idx_transfer_logs_culture ON $tableTransferLogs (culture_id)',
    );
    await db.execute(
      'CREATE INDEX idx_transfer_logs_date ON $tableTransferLogs (transfer_date)',
    );

    // Notification indexes
    await db.execute(
      'CREATE INDEX idx_notifications_culture ON $tableNotifications (culture_id)',
    );
    await db.execute(
      'CREATE INDEX idx_notifications_scheduled ON $tableNotifications (scheduled_date)',
    );
    await db.execute(
      'CREATE INDEX idx_notifications_sent ON $tableNotifications (is_sent)',
    );
  }

  /// Seed initial data (templates, etc.)
  Future<void> _seedInitialData(Database db) async {
    // Insert common media recipe templates
    await _insertRecipeTemplates(db);
  }

  /// Insert common media recipe templates
  Future<void> _insertRecipeTemplates(Database db) async {
    // Check if templates already exist to avoid duplicates
    final existingTemplates = await db.query(
      tableRecipes,
      where: 'is_template = ?',
      whereArgs: [1],
    );

    if (existingTemplates.isNotEmpty) {
      return; // Templates already inserted
    }

    final templates = [
      // INITIATION MEDIA
      {
        'name': 'MS Medium (Murashige & Skoog)',
        'description':
            'Standard tissue culture medium for most plant species initiation',
        'media_type': 'initiation',
        'ingredients_json': '''
{
  "MS_salts": {"amount": 4.4, "unit": "g/L", "notes": "Complete MS macro and micro salts"},
  "sucrose": {"amount": 30, "unit": "g/L", "notes": "Carbon source"},
  "agar": {"amount": 8, "unit": "g/L", "notes": "Gelling agent"},
  "myo_inositol": {"amount": 100, "unit": "mg/L", "notes": "Growth factor"},
  "thiamine_HCl": {"amount": 0.1, "unit": "mg/L", "notes": "Vitamin B1"},
  "pH": {"amount": 5.8, "unit": "", "notes": "Adjust before autoclaving"}
}''',
        'instructions':
            '1. Dissolve MS salts in 800ml distilled water\n2. Add sucrose and myo-inositol, mix well\n3. Add thiamine HCl\n4. Adjust pH to 5.8 with 1N NaOH or HCl\n5. Make up to 1L with distilled water\n6. Add agar and heat to dissolve\n7. Autoclave at 121°C for 15 minutes',
        'source': 'Murashige & Skoog (1962)',
        'is_template': 1,
      },
      {
        'name': 'B5 Medium (Gamborg)',
        'description':
            'Alternative initiation medium with different salt composition',
        'media_type': 'initiation',
        'ingredients_json': '''
{
  "B5_salts": {"amount": 3.2, "unit": "g/L", "notes": "Gamborg B5 macro and micro salts"},
  "sucrose": {"amount": 20, "unit": "g/L", "notes": "Lower sugar concentration"},
  "agar": {"amount": 8, "unit": "g/L", "notes": "Gelling agent"},
  "myo_inositol": {"amount": 100, "unit": "mg/L", "notes": "Growth factor"},
  "nicotinic_acid": {"amount": 1, "unit": "mg/L", "notes": "Vitamin"},
  "pyridoxine_HCl": {"amount": 1, "unit": "mg/L", "notes": "Vitamin B6"},
  "thiamine_HCl": {"amount": 10, "unit": "mg/L", "notes": "Vitamin B1"},
  "pH": {"amount": 5.5, "unit": "", "notes": "Adjust before autoclaving"}
}''',
        'instructions':
            '1. Dissolve B5 salts in 800ml distilled water\n2. Add sucrose, myo-inositol, and vitamins\n3. Adjust pH to 5.5\n4. Make up to 1L with distilled water\n5. Add agar and dissolve by heating\n6. Autoclave at 121°C for 15 minutes',
        'source': 'Gamborg et al. (1968)',
        'is_template': 1,
      },

      // MULTIPLICATION MEDIA
      {
        'name': 'WPM Medium (Woody Plant Medium)',
        'description':
            'Specialized medium for woody plant tissue culture and multiplication',
        'media_type': 'multiplication',
        'ingredients_json': '''
{
  "WPM_salts": {"amount": 4.0, "unit": "g/L", "notes": "Complete WPM macro and micro salts"},
  "sucrose": {"amount": 20, "unit": "g/L", "notes": "Reduced sugar for woody plants"},
  "agar": {"amount": 6, "unit": "g/L", "notes": "Lower agar concentration"},
  "myo_inositol": {"amount": 100, "unit": "mg/L", "notes": "Growth factor"},
  "thiamine_HCl": {"amount": 1, "unit": "mg/L", "notes": "Vitamin B1"},
  "BAP": {"amount": 1, "unit": "mg/L", "notes": "Cytokinin for shoot multiplication"},
  "pH": {"amount": 5.2, "unit": "", "notes": "Lower pH for woody plants"}
}''',
        'instructions':
            '1. Dissolve WPM salts in 800ml distilled water\n2. Add sucrose and myo-inositol\n3. Add thiamine HCl and BAP\n4. Adjust pH to 5.2\n5. Make up to 1L with distilled water\n6. Add agar and dissolve by heating\n7. Autoclave at 121°C for 15 minutes',
        'source': 'Lloyd & McCown (1980)',
        'is_template': 1,
      },
      {
        'name': 'MS Multiplication Medium',
        'description': 'MS medium with cytokinins for shoot multiplication',
        'media_type': 'multiplication',
        'ingredients_json': '''
{
  "MS_salts": {"amount": 4.4, "unit": "g/L", "notes": "Complete MS macro and micro salts"},
  "sucrose": {"amount": 30, "unit": "g/L", "notes": "Carbon source"},
  "agar": {"amount": 8, "unit": "g/L", "notes": "Gelling agent"},
  "myo_inositol": {"amount": 100, "unit": "mg/L", "notes": "Growth factor"},
  "thiamine_HCl": {"amount": 0.1, "unit": "mg/L", "notes": "Vitamin B1"},
  "BAP": {"amount": 0.5, "unit": "mg/L", "notes": "Cytokinin for multiplication"},
  "NAA": {"amount": 0.1, "unit": "mg/L", "notes": "Auxin for balanced growth"},
  "pH": {"amount": 5.8, "unit": "", "notes": "Standard pH"}
}''',
        'instructions':
            '1. Prepare MS base medium as standard\n2. Add BAP and NAA from stock solutions\n3. Adjust pH to 5.8\n4. Add agar and autoclave at 121°C for 15 minutes\n5. Pour into sterile containers under laminar flow',
        'source': 'Modified MS medium',
        'is_template': 1,
      },

      // ROOTING MEDIA
      {
        'name': 'Half-Strength MS Rooting Medium',
        'description': 'Reduced salt medium with auxin for root development',
        'media_type': 'rooting',
        'ingredients_json': '''
{
  "MS_salts_half": {"amount": 2.2, "unit": "g/L", "notes": "Half-strength MS salts"},
  "sucrose": {"amount": 15, "unit": "g/L", "notes": "Reduced sugar concentration"},
  "agar": {"amount": 6, "unit": "g/L", "notes": "Lower agar for root penetration"},
  "myo_inositol": {"amount": 100, "unit": "mg/L", "notes": "Growth factor"},
  "thiamine_HCl": {"amount": 0.1, "unit": "mg/L", "notes": "Vitamin B1"},
  "IBA": {"amount": 0.5, "unit": "mg/L", "notes": "Auxin for root induction"},
  "pH": {"amount": 5.8, "unit": "", "notes": "Standard pH"}
}''',
        'instructions':
            '1. Use half-strength MS salts (2.2 g/L)\n2. Add reduced sucrose (15 g/L)\n3. Add myo-inositol and thiamine\n4. Add IBA from stock solution\n5. Adjust pH to 5.8\n6. Add agar and dissolve\n7. Autoclave at 121°C for 15 minutes',
        'source': 'Standard rooting protocol',
        'is_template': 1,
      },
      {
        'name': 'NAA Rooting Medium',
        'description': 'Alternative rooting medium using NAA as auxin source',
        'media_type': 'rooting',
        'ingredients_json': '''
{
  "MS_salts_half": {"amount": 2.2, "unit": "g/L", "notes": "Half-strength MS salts"},
  "sucrose": {"amount": 20, "unit": "g/L", "notes": "Carbon source"},
  "agar": {"amount": 6, "unit": "g/L", "notes": "Gelling agent"},
  "myo_inositol": {"amount": 100, "unit": "mg/L", "notes": "Growth factor"},
  "thiamine_HCl": {"amount": 0.1, "unit": "mg/L", "notes": "Vitamin B1"},
  "NAA": {"amount": 1, "unit": "mg/L", "notes": "Strong auxin for difficult-to-root species"},
  "pH": {"amount": 5.8, "unit": "", "notes": "Standard pH"}
}''',
        'instructions':
            '1. Prepare half-strength MS base\n2. Add sucrose and vitamins\n3. Add NAA from stock solution\n4. Adjust pH to 5.8\n5. Add agar and autoclave\n6. Use for species requiring stronger auxin',
        'source': 'Modified rooting protocol',
        'is_template': 1,
      },

      // ACCLIMATIZATION MEDIA
      {
        'name': 'Acclimatization Medium',
        'description':
            'Hormone-free medium for plant hardening before transplant',
        'media_type': 'acclimatization',
        'ingredients_json': '''
{
  "MS_salts_quarter": {"amount": 1.1, "unit": "g/L", "notes": "Quarter-strength MS salts"},
  "sucrose": {"amount": 10, "unit": "g/L", "notes": "Minimal sugar for weaning"},
  "agar": {"amount": 4, "unit": "g/L", "notes": "Soft gel for easy removal"},
  "myo_inositol": {"amount": 50, "unit": "mg/L", "notes": "Reduced growth factors"},
  "pH": {"amount": 6.0, "unit": "", "notes": "Slightly higher pH"}
}''',
        'instructions':
            '1. Use quarter-strength MS salts\n2. Add minimal sucrose (10 g/L)\n3. Add reduced myo-inositol\n4. Adjust pH to 6.0\n5. Add minimal agar for soft gel\n6. Autoclave and use for final stage before transplant',
        'source': 'Standard acclimatization protocol',
        'is_template': 1,
      },
    ];

    for (final template in templates) {
      await db.insert(tableRecipes, template);
    }
  }

  /// Drop all tables (for migrations)
  Future<void> _dropTables(Database db) async {
    await db.execute('DROP TABLE IF EXISTS $tableNotifications');
    await db.execute('DROP TABLE IF EXISTS $tableTransferLogs');
    await db.execute('DROP TABLE IF EXISTS $tableRecipes');
    await db.execute('DROP TABLE IF EXISTS $tableCultures');
  }

  /// Close database connection
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  /// Get database path for debugging
  Future<String> getDatabasePath() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    return join(documentsDirectory.path, _databaseName);
  }

  /// Delete database file (for testing/reset)
  Future<void> deleteDatabase() async {
    String path = await getDatabasePath();
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
