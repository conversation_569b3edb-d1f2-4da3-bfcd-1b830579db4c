import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:culture_track/database/database_helper.dart';
import 'package:culture_track/providers/recipe_provider.dart';

void main() {
  // Initialize FFI for testing
  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  group('Template Recipes Tests', () {
    late DatabaseHelper databaseHelper;
    late RecipeProvider recipeProvider;

    setUp(() async {
      databaseHelper = DatabaseHelper();
      recipeProvider = RecipeProvider();

      // Delete any existing database to start fresh
      await databaseHelper.deleteDatabase();
    });

    tearDown(() async {
      await databaseHelper.close();
    });

    test(
      'should insert template recipes during database initialization',
      () async {
        // Initialize database (this should trigger template insertion)
        final db = await databaseHelper.database;

        // Query for template recipes
        final templates = await db.query(
          DatabaseHelper.tableRecipes,
          where: 'is_template = ?',
          whereArgs: [1],
          orderBy: 'media_type, name',
        );

        // Verify templates were inserted
        expect(templates.isNotEmpty, true);
        expect(
          templates.length,
          greaterThanOrEqualTo(7),
        ); // We added 7 templates

        // Verify we have templates for all media types
        final mediaTypes = templates.map((t) => t['media_type']).toSet();
        expect(mediaTypes.contains('initiation'), true);
        expect(mediaTypes.contains('multiplication'), true);
        expect(mediaTypes.contains('rooting'), true);
        expect(mediaTypes.contains('acclimatization'), true);
      },
    );

    test(
      'should not insert duplicate templates on subsequent initializations',
      () async {
        // Initialize database first time
        await databaseHelper.database;

        // Get initial template count
        final db = await databaseHelper.database;
        final initialTemplates = await db.query(
          DatabaseHelper.tableRecipes,
          where: 'is_template = ?',
          whereArgs: [1],
        );
        final initialCount = initialTemplates.length;

        // Force re-initialization (simulate app restart)
        await databaseHelper.close();
        await databaseHelper.database;

        // Check template count again
        final finalTemplates = await db.query(
          DatabaseHelper.tableRecipes,
          where: 'is_template = ?',
          whereArgs: [1],
        );

        expect(finalTemplates.length, equals(initialCount));
      },
    );

    test('should load template recipes through RecipeProvider', () async {
      // Initialize database
      await databaseHelper.database;

      // Load recipes through provider
      await recipeProvider.loadRecipes();

      // Verify templates are loaded
      final templates = recipeProvider.templateRecipes;
      expect(templates.isNotEmpty, true);
      expect(templates.length, greaterThanOrEqualTo(7));

      // Verify all templates have isTemplate = true
      for (final template in templates) {
        expect(template.isTemplate, true);
      }
    });

    test('should have properly structured template recipes', () async {
      await databaseHelper.database;
      await recipeProvider.loadRecipes();

      final templates = recipeProvider.templateRecipes;

      for (final template in templates) {
        // Verify required fields
        expect(template.name.isNotEmpty, true);
        expect(template.description?.isNotEmpty, true);
        expect(template.mediaType.isNotEmpty, true);
        expect(template.ingredients.isNotEmpty, true);
        expect(template.instructions?.isNotEmpty, true);
        expect(template.source?.isNotEmpty, true);
        expect(template.isTemplate, true);

        // Verify ingredients have proper structure
        for (final ingredient in template.ingredients.values) {
          expect(ingredient.amount, greaterThan(0));
          expect(ingredient.unit.isNotEmpty, true);
        }
      }
    });

    test('should have specific expected template recipes', () async {
      await databaseHelper.database;
      await recipeProvider.loadRecipes();

      final templates = recipeProvider.templateRecipes;
      final templateNames = templates.map((t) => t.name).toList();

      // Verify specific templates exist
      expect(templateNames.contains('MS Medium (Murashige & Skoog)'), true);
      expect(templateNames.contains('B5 Medium (Gamborg)'), true);
      expect(templateNames.contains('WPM Medium (Woody Plant Medium)'), true);
      expect(templateNames.contains('MS Multiplication Medium'), true);
      expect(templateNames.contains('Half-Strength MS Rooting Medium'), true);
      expect(templateNames.contains('NAA Rooting Medium'), true);
      expect(templateNames.contains('Acclimatization Medium'), true);
    });

    test('should have templates for each culture stage', () async {
      await databaseHelper.database;
      await recipeProvider.loadRecipes();

      final templates = recipeProvider.templateRecipes;

      // Group templates by media type
      final initiationTemplates =
          templates.where((t) => t.mediaType == 'initiation').toList();
      final multiplicationTemplates =
          templates.where((t) => t.mediaType == 'multiplication').toList();
      final rootingTemplates =
          templates.where((t) => t.mediaType == 'rooting').toList();
      final acclimatizationTemplates =
          templates.where((t) => t.mediaType == 'acclimatization').toList();

      // Verify we have templates for each stage
      expect(initiationTemplates.length, greaterThanOrEqualTo(2));
      expect(multiplicationTemplates.length, greaterThanOrEqualTo(2));
      expect(rootingTemplates.length, greaterThanOrEqualTo(2));
      expect(acclimatizationTemplates.length, greaterThanOrEqualTo(1));
    });

    test('should have detailed ingredient information with notes', () async {
      await databaseHelper.database;
      await recipeProvider.loadRecipes();

      final templates = recipeProvider.templateRecipes;
      final msTemplate = templates.firstWhere(
        (t) => t.name == 'MS Medium (Murashige & Skoog)',
      );

      // Verify MS template has expected ingredients with notes
      expect(msTemplate.ingredients.containsKey('MS_salts'), true);
      expect(msTemplate.ingredients.containsKey('sucrose'), true);
      expect(msTemplate.ingredients.containsKey('agar'), true);
      expect(msTemplate.ingredients.containsKey('myo_inositol'), true);
      expect(msTemplate.ingredients.containsKey('thiamine_HCl'), true);
      expect(msTemplate.ingredients.containsKey('pH'), true);

      // Verify ingredients have notes
      final msSalts = msTemplate.ingredients['MS_salts']!;
      expect(msSalts.notes?.isNotEmpty, true);
      expect(msSalts.notes, contains('Complete MS macro and micro salts'));
    });

    test('should be accessible through recipe filtering methods', () async {
      await databaseHelper.database;
      await recipeProvider.loadRecipes();

      // Test filtering by media type
      final initiationRecipes = recipeProvider.getRecipesByMediaType(
        'initiation',
      );
      final multiplicationRecipes = recipeProvider.getRecipesByMediaType(
        'multiplication',
      );
      final rootingRecipes = recipeProvider.getRecipesByMediaType('rooting');
      final acclimatizationRecipes = recipeProvider.getRecipesByMediaType(
        'acclimatization',
      );

      expect(initiationRecipes.isNotEmpty, true);
      expect(multiplicationRecipes.isNotEmpty, true);
      expect(rootingRecipes.isNotEmpty, true);
      expect(acclimatizationRecipes.isNotEmpty, true);

      // Verify template count matches expectations
      expect(recipeProvider.templatesCount, greaterThanOrEqualTo(7));
    });
  });
}
